{"name": "vite-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint:fix": "prettier --write ."}, "dependencies": {"@duo-common/config-center": "^0.0.8", "@types/lodash-es": "^4.17.9", "core-js": "^3.33.2", "dayjs": "^1.11.9", "echarts": "^5.4.3", "element-plus": "^2.3.14", "lodash-es": "^4.17.21", "ufo": "^1.3.1", "vue": "^3.3.4", "vue3-oidc": "^0.1.14", "xlsx": "^0.18.5"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "^4.1.0", "@iconify/vue": "^4.1.1", "@types/node": "^18.17.17", "@vitejs/plugin-legacy": "^4.1.1", "@vitejs/plugin-vue": "^4.2.3", "@vitejs/plugin-vue-jsx": "^3.0.2", "@vueuse/core": "^10.4.1", "less": "^4.2.0", "prettier": "^3.1.0", "regenerator-runtime": "^0.14.0", "terser": "^5.24.0", "typescript": "^5.0.2", "unocss": "^0.55.7", "unplugin-auto-import": "^0.16.6", "unplugin-element-plus": "^0.8.0", "unplugin-vue-components": "^0.25.2", "vconsole": "^3.15.1", "vite": "^4.4.5", "vite-plugin-inspect": "^0.7.38", "vite-plugin-pages": "^0.31.0", "vue-router": "^4.2.4", "vue-tsc": "^1.8.5"}}